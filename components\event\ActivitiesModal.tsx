import React from "react";
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from "react-native";
import { MyEventType, EventActivityType } from "@/features/events/model";
import { Feather } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import Gap from "../Gap";
import Button from "../Button";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

type Props = {
  event: MyEventType;
  activities: EventActivityType[];
  isOpen: boolean;
  onClose: () => void;
};

const ActivityCard = ({ activity }: { activity: EventActivityType }) => {
  const { t } = useTranslation();

  const formatTime = (dateTime: string) => {
    return dayjs(dateTime).format("HH:mm");
  };

  const formatDate = (dateTime: string) => {
    return dayjs(dateTime).format("DD/MM");
  };

  return (
    <View style={styles.activityCard}>
      <Text style={styles.activityDescription}>{activity.description}</Text>
      <Gap y={globalStyles.gap["2xs"]} />

      <View style={styles.activityInfo}>
        <View style={styles.infoRow}>
          <Feather
            name="clock"
            size={16}
            color={globalStyles.colors.dark.secondary}
          />
          <Gap x={globalStyles.gap["2xs"]} />
          <Text style={styles.infoText}>
            {formatTime(activity.startTime)}
            {activity.endTime && ` - ${formatTime(activity.endTime)}`}
            <Text style={styles.dateText}>
              {" "}
              ({formatDate(activity.startTime)})
            </Text>
          </Text>
        </View>

        {activity.location && (
          <>
            <Gap y={globalStyles.gap["2xs"]} />
            <View style={styles.infoRow}>
              <Feather
                name="map-pin"
                size={16}
                color={globalStyles.colors.dark.secondary}
              />
              <Gap x={globalStyles.gap["2xs"]} />
              <Text style={styles.infoText}>{activity.location}</Text>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

const ActivitiesModal = ({ event, activities, isOpen, onClose }: Props) => {
  const { t } = useTranslation();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isOpen}
      onRequestClose={onClose}
      presentationStyle={Platform.OS === "ios" ? "pageSheet" : "overFullScreen"}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.header}>
            <Text style={styles.title}>{t("event.activities")}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Feather
                name="x"
                size={24}
                color={globalStyles.colors.dark.secondary}
              />
            </TouchableOpacity>
          </View>

          <Text style={styles.eventName}>{event.name}</Text>
          <Gap y={globalStyles.gap.xs} />

          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {activities.map((activity, index) => (
              <View key={index}>
                <ActivityCard activity={activity} />
                {index < activities.length - 1 && (
                  <Gap y={globalStyles.gap.xs} />
                )}
              </View>
            ))}
            <Gap y={globalStyles.gap.lg} />
          </ScrollView>

          <Button
            text={t("common.back")}
            onPress={onClose}
            type="secondary"
            style={styles.backButton}
          />
        </View>
      </View>
      {Platform.OS !== "ios" && <View style={styles.backdrop} />}
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  modalView: {
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: globalStyles.gap.sm,
    paddingTop: globalStyles.gap.xs,
    width: "100%",
    maxHeight: "85%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  backdrop: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: globalStyles.gap["2xs"],
  },
  title: {
    fontSize: globalStyles.size.xl,
    fontWeight: "bold",
    color: globalStyles.colors.dark.primary,
  },
  closeButton: {
    padding: globalStyles.gap["2xs"],
  },
  eventName: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.dark.secondary,
    fontWeight: "600",
  },
  scrollView: {
    flex: 1,
  },
  activityCard: {
    backgroundColor: globalStyles.colors.light.primary,
    borderRadius: 12,
    padding: globalStyles.gap.sm,
    borderWidth: 1,
    borderColor: globalStyles.colors.light.secondary,
  },
  activityDescription: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.dark.primary,
    fontWeight: "500",
    lineHeight: 20,
  },
  activityInfo: {
    marginTop: globalStyles.gap["2xs"],
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoText: {
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.dark.secondary,
    flex: 1,
  },
  dateText: {
    fontSize: globalStyles.size.xs,
    color: globalStyles.colors.tertiary2,
  },
  backButton: {
    marginTop: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
  },
});

export default ActivitiesModal;
